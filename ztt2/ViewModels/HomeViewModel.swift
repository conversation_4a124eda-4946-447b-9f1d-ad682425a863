//
//  HomeViewModel.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI
import Combine

/**
 * 首页视图模型
 * 管理首页的状态和业务逻辑
 */
class HomeViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前选中的Tab索引
    @Published var selectedTabIndex: Int = 0
    
    /// 家庭成员列表
    @Published var familyMembers: [FamilyMember] = []
    
    /// 全家总积分
    @Published var totalFamilyScore: Int = 0
    
    /// 是否正在加载
    @Published var isLoading: Bool = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        // 延迟加载数据，避免启动时的问题
        DispatchQueue.main.async {
            self.loadFamilyMembers()
            self.calculateTotalScore()
        }
    }
    
    // MARK: - Public Methods
    
    /**
     * 选择Tab
     * @param index Tab索引
     */
    func selectTab(_ index: Int) {
        selectedTabIndex = index
    }
    
    /**
     * 添加家庭成员
     * @param member 成员信息
     */
    func addFamilyMember(_ member: FamilyMember) {
        familyMembers.append(member)
        calculateTotalScore()
    }
    
    /**
     * 删除家庭成员
     * @param memberId 成员ID
     */
    func deleteFamilyMember(memberId: String) {
        familyMembers.removeAll { $0.id == memberId }
        calculateTotalScore()
    }
    
    /**
     * 更新成员积分
     * @param memberId 成员ID
     * @param score 新积分
     */
    func updateMemberScore(memberId: String, score: Int) {
        if let index = familyMembers.firstIndex(where: { $0.id == memberId }) {
            familyMembers[index].currentScore = score
            calculateTotalScore()
        }
    }
    
    /**
     * 刷新数据
     */
    func refresh() {
        loadFamilyMembers()
        calculateTotalScore()
    }
    
    // MARK: - Private Methods
    
    /**
     * 加载家庭成员数据
     */
    private func loadFamilyMembers() {
        isLoading = true
        
        // TODO: 从CoreData加载数据
        // 这里暂时使用空数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.familyMembers = []
            self.isLoading = false
        }
    }
    
    /**
     * 计算全家总积分
     */
    private func calculateTotalScore() {
        totalFamilyScore = familyMembers.reduce(0) { $0 + $1.currentScore }
    }
}

// MARK: - Family Member Model

/**
 * 家庭成员数据模型
 */
struct FamilyMember: Identifiable, Codable {
    let id: String
    var name: String
    var role: FamilyRole
    var currentScore: Int
    var birthDate: Date?
    var avatarImageName: String
    
    init(id: String = UUID().uuidString, name: String, role: FamilyRole, currentScore: Int = 0, birthDate: Date? = nil) {
        self.id = id
        self.name = name
        self.role = role
        self.currentScore = currentScore
        self.birthDate = birthDate
        self.avatarImageName = role.defaultAvatarName
    }
}

/**
 * 家庭角色枚举
 */
enum FamilyRole: String, CaseIterable, Codable {
    case father = "爸爸"
    case mother = "妈妈"
    case son = "儿子"
    case daughter = "女儿"
    case other = "其他"
    
    var defaultAvatarName: String {
        switch self {
        case .father:
            return "person.fill"
        case .mother:
            return "person.fill"
        case .son:
            return "person.fill"
        case .daughter:
            return "person.fill"
        case .other:
            return "person.fill"
        }
    }
    
    var isChild: Bool {
        return self == .son || self == .daughter
    }
}
