//
//  HomeViewModel.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI
import Combine
import CoreData

/**
 * 首页视图模型
 * 管理首页的状态和业务逻辑
 */
class HomeViewModel: ObservableObject {
    
    // MARK: - Published Properties

    /// 当前选中的Tab索引
    @Published var selectedTabIndex: Int = 0

    /// 家庭成员列表
    @Published var members: [Member] = []

    /// 全家总积分
    @Published var totalFamilyScore: Int32 = 0

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 当前用户
    @Published var currentUser: User?

    // MARK: - Private Properties

    private var cancellables = Set<AnyCancellable>()
    private let dataManager = DataManager.shared
    
    // MARK: - Initialization

    init() {
        setupDataObservation()
        loadData()
    }
    
    // MARK: - Public Methods

    /**
     * 选择Tab
     * @param index Tab索引
     */
    func selectTab(_ index: Int) {
        selectedTabIndex = index
    }

    /**
     * 添加家庭成员
     * @param name 成员姓名
     * @param role 成员角色
     * @param birthDate 出生日期
     */
    func addFamilyMember(name: String, role: String, birthDate: Date? = nil) {
        let _ = dataManager.createMember(
            name: name,
            role: role,
            birthDate: birthDate,
            initialPoints: 0
        )
        loadData()
    }

    /**
     * 删除家庭成员
     * @param member 要删除的成员
     */
    func deleteFamilyMember(_ member: Member) {
        dataManager.deleteMember(member)
        loadData()
    }

    /**
     * 更新成员积分
     * @param member 成员
     * @param reason 原因
     * @param value 积分变化值
     */
    func updateMemberPoints(member: Member, reason: String, value: Int32) {
        dataManager.addPointRecord(to: member, reason: reason, value: value)
        loadData()
    }

    /**
     * 刷新数据
     */
    func refresh() {
        loadData()
    }
    
    // MARK: - Private Methods

    /**
     * 设置数据观察
     */
    private func setupDataObservation() {
        // 观察DataManager的数据变化
        dataManager.$members
            .receive(on: DispatchQueue.main)
            .sink { [weak self] members in
                self?.members = members
                self?.calculateTotalScore()
            }
            .store(in: &cancellables)

        dataManager.$currentUser
            .receive(on: DispatchQueue.main)
            .assign(to: \.currentUser, on: self)
            .store(in: &cancellables)
    }

    /**
     * 加载数据
     */
    private func loadData() {
        isLoading = true

        DispatchQueue.main.async {
            // DataManager会自动更新数据，通过观察者模式更新UI
            self.isLoading = false
        }
    }

    /**
     * 计算全家总积分
     */
    private func calculateTotalScore() {
        totalFamilyScore = members.reduce(0) { $0 + $1.currentPoints }
    }
}
