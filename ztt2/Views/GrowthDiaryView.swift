//
//  GrowthDiaryView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 成长日记页面
 * 根据需求文档设计，包含大输入框、时间选择器、保存和查看历史按钮
 */
struct GrowthDiaryView: View {

    // MARK: - State Properties
    @State private var diaryContent: String = ""
    @State private var selectedDate: Date = Date()
    @State private var showDatePicker: Bool = false
    @State private var isLoading: Bool = false
    @State private var showSuccessAlert: Bool = false
    @State private var showErrorAlert: Bool = false
    @State private var errorMessage: String = ""
    @State private var pageAppeared: Bool = false

    // 选择对象相关状态
    @State private var selectedMember: FamilyMember? = nil
    @State private var showMemberPicker: Bool = false

    // 历史日记查看相关状态
    @State private var showHistoryMemberPicker: Bool = false
    @State private var showHistorySheet: Bool = false
    @State private var selectedHistoryMember: FamilyMember? = nil
    @State private var selectedReportType: Int = 0 // 0: 分析报告, 1: 成长报告

    // 临时示例数据 - 后续会替换为CoreData
    private let children: [FamilyMember] = [
        FamilyMember(name: "小明", role: .son),
        FamilyMember(name: "小红", role: .daughter)
    ]

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 美化背景渐变 - 与首页保持一致的风格
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "#fcfff4"), location: 0.0),
                        .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                        .init(color: Color.white, location: 0.7),
                        .init(color: Color(hex: "#fafffe"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(.all)

                // 装饰性背景元素
                VStack {
                    HStack {
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.03))
                            .frame(width: 100, height: 100)
                            .offset(x: -30, y: 20)
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.04))
                            .frame(width: 120, height: 120)
                            .offset(x: 40, y: -10)
                    }
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.02))
                            .frame(width: 80, height: 80)
                            .offset(x: 20, y: 30)
                    }
                }

                // 主要内容区域
                VStack(spacing: 0) {
                    // 标题区域
                    titleSection

                    // 内容区域
                    contentSection(geometry: geometry)
                }
                .padding(.top, DesignSystem.Spacing.lg)
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.bottom, DesignSystem.Spacing.md)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6)) {
                pageAppeared = true
            }
        }
        .alert("growth_diary.save.success".localized, isPresented: $showSuccessAlert) {
            Button("common.button.confirm".localized) { }
        }
        .alert("growth_diary.save.error".localized, isPresented: $showErrorAlert) {
            Button("common.button.confirm".localized) { }
        } message: {
            Text(errorMessage)
        }
        .overlay(
            // 日期选择器弹窗
            DatePickerPopupView(
                isPresented: $showDatePicker,
                selectedDate: $selectedDate,
                onConfirm: {
                    // 日期选择确认后的处理
                }
            )
        )
        .overlay(
            // 成员选择器弹窗
            MemberPickerPopupView(
                isPresented: $showMemberPicker,
                selectedMember: $selectedMember,
                children: children,
                onConfirm: {
                    // 成员选择确认后的处理
                }
            )
        )
        .sheet(isPresented: $showHistoryMemberPicker) {
            historyMemberPickerSheet
        }
        .sheet(isPresented: $showHistorySheet) {
            historyReportSheet
        }
    }

    // MARK: - Title Section
    private var titleSection: some View {
        HStack {
            Text("growth_diary.title".localized)
                .font(.system(
                    size: DesignSystem.Typography.HeadingLarge.fontSize,
                    weight: DesignSystem.Typography.HeadingLarge.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : -20)
                .animation(.easeOut(duration: 0.8).delay(0.2), value: pageAppeared)

            Spacer()
        }
        .padding(.bottom, DesignSystem.Spacing.lg)
    }

    // MARK: - Content Section
    private func contentSection(geometry: GeometryProxy) -> some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 大输入框 - 占屏幕高度的50%
            diaryInputSection(geometry: geometry)

            // 时间选择器和对象选择器
            dateAndMemberSelectionSection

            // 按钮区域
            buttonSection

            Spacer()
        }
        .opacity(pageAppeared ? 1.0 : 0.0)
        .offset(y: pageAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.4), value: pageAppeared)
    }

    // MARK: - Diary Input Section
    private func diaryInputSection(geometry: GeometryProxy) -> some View {
        let inputHeight = geometry.size.height * 0.5

        return VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            ZStack(alignment: .topLeading) {
                // 背景
                RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                    .foregroundColor(DesignSystem.Colors.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                            .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 2)
                    )
                    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)

                // 输入框
                TextEditor(text: $diaryContent)
                    .font(.system(size: DesignSystem.Typography.Body.fontSize))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(DesignSystem.Spacing.md)
                    .background(Color.clear)

                // 占位符
                if diaryContent.isEmpty {
                    Text("growth_diary.input.placeholder".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(.top, DesignSystem.Spacing.md + 8)
                        .padding(.leading, DesignSystem.Spacing.md + 4)
                        .allowsHitTesting(false)
                }
            }
            .frame(height: inputHeight)
        }
    }

    // MARK: - Date and Member Selection Section
    private var dateAndMemberSelectionSection: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            // 记录时间按钮 - 减少宽度
            Button(action: {
                showDatePicker = true
            }) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "calendar")
                        .foregroundColor(Color(hex: "#B5E36B"))
                        .font(.system(size: 16, weight: .medium))

                    VStack(alignment: .leading, spacing: 2) {
                        Text(formatDate(selectedDate))
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color(hex: "#B5E36B"))
                    }

                    Image(systemName: "chevron.right")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.system(size: 12, weight: .medium))
                }
                .padding(.horizontal, DesignSystem.Spacing.sm)
                .padding(.vertical, DesignSystem.Spacing.sm)
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.md)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
            .frame(maxWidth: .infinity) // 占用可用空间的一半

            // 选择对象按钮
            Button(action: {
                showMemberPicker = true
            }) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "person.circle")
                        .foregroundColor(Color(hex: "#B5E36B"))
                        .font(.system(size: 16, weight: .medium))

                    VStack(alignment: .leading, spacing: 2) {
                        Text(selectedMember?.name ?? "growth_diary.member.select".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color(hex: "#B5E36B"))
                            .lineLimit(1)
                    }

                    Image(systemName: "chevron.right")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.system(size: 12, weight: .medium))
                }
                .padding(.horizontal, DesignSystem.Spacing.sm)
                .padding(.vertical, DesignSystem.Spacing.sm)
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.md)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
            .frame(maxWidth: .infinity) // 占用可用空间的一半
        }
    }

    // MARK: - Button Section
    private var buttonSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // 保存日记按钮
            Button(action: saveDiary) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "square.and.arrow.down")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text("growth_diary.button.save".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize, weight: .medium))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color(hex: "#B5E36B"))
                .cornerRadius(DesignSystem.Radius.md)
                .shadow(color: Color(hex: "#B5E36B").opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(isLoading || diaryContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            .opacity(diaryContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.6 : 1.0)

            // 查看历史日记按钮
            Button(action: viewHistoryDiary) {
                HStack {
                    Image(systemName: "book")
                        .font(.system(size: 16, weight: .medium))

                    Text("growth_diary.button.view_history".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize, weight: .medium))
                }
                .foregroundColor(Color(hex: "#B5E36B"))
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.md)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                        .stroke(Color(hex: "#B5E36B"), lineWidth: 2)
                )
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
        }
    }

    // MARK: - History Member Picker Sheet
    private var historyMemberPickerSheet: some View {
        NavigationView {
            VStack(spacing: DesignSystem.Spacing.md) {
                if children.isEmpty {
                    // 没有孩子时的提示
                    VStack(spacing: DesignSystem.Spacing.md) {
                        Image(systemName: "person.badge.plus")
                            .font(.system(size: 48))
                            .foregroundColor(DesignSystem.Colors.textSecondary)

                        Text("growth_diary.history.empty".localized)
                            .font(.system(size: DesignSystem.Typography.Body.fontSize))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                } else {
                    // 孩子列表
                    LazyVStack(spacing: DesignSystem.Spacing.sm) {
                        ForEach(children, id: \.id) { child in
                            Button(action: {
                                selectedHistoryMember = child
                                showHistoryMemberPicker = false
                                showHistorySheet = true
                            }) {
                                HStack {
                                    // 头像
                                    Image(child.avatarImageName)
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: 40, height: 40)
                                        .clipShape(Circle())

                                    VStack(alignment: .leading, spacing: 2) {
                                        Text(child.name)
                                            .font(.system(size: DesignSystem.Typography.Body.fontSize, weight: .medium))
                                            .foregroundColor(DesignSystem.Colors.textPrimary)

                                        Text(child.role.rawValue)
                                            .font(.system(size: 12))
                                            .foregroundColor(DesignSystem.Colors.textSecondary)
                                    }

                                    Spacer()

                                    Image(systemName: "chevron.right")
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                        .font(.system(size: 14))
                                }
                                .padding(DesignSystem.Spacing.md)
                                .background(DesignSystem.Colors.cardBackground)
                                .cornerRadius(DesignSystem.Radius.md)
                                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal)
                }

                Spacer()
            }
            .navigationTitle("growth_diary.history.title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(
                leading: Button("common.button.cancel".localized) {
                    showHistoryMemberPicker = false
                }
            )
        }
    }

    // MARK: - History Report Sheet
    private var historyReportSheet: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 分段选项卡
                Picker("报告类型", selection: $selectedReportType) {
                    Text("growth_diary.report.analysis".localized).tag(0)
                    Text("growth_diary.report.growth".localized).tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                .padding(.top)

                // 内容区域
                TabView(selection: $selectedReportType) {
                    // 分析报告页面
                    analysisReportView
                        .tag(0)

                    // 成长报告页面
                    growthReportView
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle(selectedHistoryMember?.name ?? "")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(
                leading: Button("common.button.close".localized) {
                    showHistorySheet = false
                }
            )
        }
    }

    // MARK: - Analysis Report View
    private var analysisReportView: some View {
        ScrollView {
            VStack(spacing: DesignSystem.Spacing.lg) {
                // 占位内容 - 分析报告
                VStack(spacing: DesignSystem.Spacing.md) {
                    Image(systemName: "chart.bar.doc.horizontal")
                        .font(.system(size: 48))
                        .foregroundColor(Color(hex: "#B5E36B"))

                    Text("growth_diary.report.analysis.title".localized)
                        .font(.system(size: DesignSystem.Typography.HeadingMedium.fontSize, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("growth_diary.report.analysis.description".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding()
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.lg)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)

                Spacer()
            }
            .padding()
        }
    }

    // MARK: - Growth Report View
    private var growthReportView: some View {
        ScrollView {
            VStack(spacing: DesignSystem.Spacing.lg) {
                // 占位内容 - 成长报告
                VStack(spacing: DesignSystem.Spacing.md) {
                    Image(systemName: "heart.text.square")
                        .font(.system(size: 48))
                        .foregroundColor(Color(hex: "#FFE49E"))

                    Text("growth_diary.report.growth.title".localized)
                        .font(.system(size: DesignSystem.Typography.HeadingMedium.fontSize, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("growth_diary.report.growth.description".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding()
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.lg)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)

                Spacer()
            }
            .padding()
        }
    }

    // MARK: - Helper Methods

    /**
     * 格式化日期显示
     */
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy/M/d"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }

    /**
     * 保存日记
     */
    private func saveDiary() {
        let content = diaryContent.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !content.isEmpty else {
            errorMessage = "growth_diary.input.empty".localized
            showErrorAlert = true
            return
        }

        isLoading = true

        // 模拟保存操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false

            // TODO: 实际保存到CoreData
            print("保存日记: \(content)")
            print("日期: \(selectedDate)")
            print("选择的成员: \(selectedMember?.name ?? "无")")

            // 显示成功提示
            showSuccessAlert = true

            // 清空输入框
            diaryContent = ""
        }
    }

    /**
     * 查看历史日记
     */
    private func viewHistoryDiary() {
        showHistoryMemberPicker = true
    }
}

// MARK: - Preview
#Preview {
    GrowthDiaryView()
}
