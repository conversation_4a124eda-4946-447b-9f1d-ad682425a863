//
//  MemberPickerPopupView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 成员选择器弹窗组件
 * 使用与全员操作弹窗相同的样式和动画效果
 */
struct MemberPickerPopupView: View {
    
    @Binding var isPresented: Bool
    @Binding var selectedMember: FamilyMember?
    let children: [FamilyMember]
    let onConfirm: () -> Void
    
    @State private var animationTrigger = false
    @State private var tempSelectedMember: FamilyMember?
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 成员选择器弹窗
                VStack(spacing: 0) {
                    // 标题
                    Text("growth_diary.member.picker.title".localized)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(.top, 24)
                        .padding(.bottom, 20)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 成员列表
                    if children.isEmpty {
                        // 空状态
                        VStack(spacing: 12) {
                            Image(systemName: "person.2.slash")
                                .font(.system(size: 40))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("growth_diary.member.empty".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.vertical, 40)
                    } else {
                        ScrollView {
                            LazyVStack(spacing: 0) {
                                ForEach(children, id: \.id) { child in
                                    MemberPickerRow(
                                        member: child,
                                        isSelected: tempSelectedMember?.id == child.id,
                                        action: {
                                            tempSelectedMember = child
                                        }
                                    )
                                    
                                    if child.id != children.last?.id {
                                        Rectangle()
                                            .fill(Color(hex: "#edf5d9"))
                                            .frame(height: 1)
                                            .padding(.horizontal, 20)
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 300)
                    }
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 按钮区域
                    HStack(spacing: 0) {
                        // 取消按钮
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(maxWidth: .infinity, minHeight: 50)
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(width: 1, height: 50)
                        
                        // 确认按钮
                        Button(action: {
                            selectedMember = tempSelectedMember
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                            onConfirm()
                        }) {
                            Text("common.button.confirm".localized)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(tempSelectedMember != nil ? Color(hex: "#a9d051") : DesignSystem.Colors.textSecondary)
                                .frame(maxWidth: .infinity, minHeight: 50)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .disabled(tempSelectedMember == nil)
                    }
                }
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white)
                        .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                )
                .frame(maxWidth: 320)
                .scaleEffect(animationTrigger ? 1.0 : 0.8)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: animationTrigger)
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                tempSelectedMember = selectedMember
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
}

/**
 * 成员选择行组件
 */
struct MemberPickerRow: View {
    
    let member: FamilyMember
    let isSelected: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
                action()
            }
        }) {
            HStack(spacing: 16) {
                // 头像
                Image(member.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 44, height: 44)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(Color(hex: "#edf5d9"), lineWidth: 2)
                    )
                
                // 成员信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(member.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text(member.role.rawValue)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // 选中状态指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(Color(hex: "#a9d051"))
                } else {
                    Image(systemName: "circle")
                        .font(.system(size: 20))
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                Rectangle()
                    .fill(isPressed ? Color(hex: "#f5f5f5") : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()
        
        MemberPickerPopupView(
            isPresented: .constant(true),
            selectedMember: .constant(nil),
            children: [
                FamilyMember(name: "小明", role: .son),
                FamilyMember(name: "小红", role: .daughter)
            ],
            onConfirm: { print("成员确认") }
        )
    }
}
