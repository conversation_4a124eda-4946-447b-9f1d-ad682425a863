# 转团团 ztt2 - 液态TabBar实现

## 项目概述

这是转团团应用的第二个版本，主要特色是实现了一个具有液态融球效果的自定义TabBar。

## 主要功能

### 1. 液态TabBar
- **设计理念**: 采用液态融球效果，提供流畅的视觉体验
- **动画效果**: 选中状态时显示黄色圆球背景，未选中图标呈现灰色
- **响应式设计**: 支持iOS 15.6+，兼容iPhone和iPad

### 2. 三个主要Tab页面

#### 首页 (家庭成员管理)
- 家庭成员列表展示
- 全家总积分统计
- 添加成员功能
- 全家操作入口

#### 成长日记
- 记录孩子成长点滴
- 选择记录对象
- 日期选择功能
- 历史日记查看

#### 个人中心
- 用户信息展示
- 订阅管理
- 系统设置
- 帮助与反馈

## 技术实现

### 核心组件

1. **LiquidTabBarShape**: 自定义Shape，实现液态背景轮廓
2. **LiquidTabBarBackground**: 背景组件，包含白色背景和绿色边框
3. **LiquidTabBarBubble**: 融球组件，显示选中状态的黄色圆球
4. **CustomTabBar**: 主TabBar组件，整合所有效果

### 设计系统

- **DesignSystem.swift**: 统一的设计规范
- **颜色系统**: 基于品牌色的完整色彩方案
- **字体系统**: 统一的字体大小和权重定义
- **间距系统**: 一致的布局间距规范

### 本地化支持

- 支持中文本地化
- 可扩展的多语言架构
- 便捷的本地化字符串管理

## 文件结构

```
ztt2/
├── Views/
│   ├── MainTabView.swift          # 主导航容器
│   ├── HomeView.swift             # 首页视图
│   ├── GrowthDiaryView.swift      # 成长日记视图
│   ├── ProfileView.swift          # 个人中心视图
│   ├── MemberDetailView.swift     # 成员详情视图
│   ├── SubscriptionView.swift     # 订阅页面视图
│   └── Components/
│       ├── CustomTabBar.swift           # 自定义TabBar
│       ├── LiquidTabBarShape.swift      # 液态形状
│       ├── LiquidTabBarBackground.swift # 液态背景
│       └── LiquidTabBarBubble.swift     # 液态融球
├── Styles/
│   └── DesignSystem.swift         # 设计系统
├── Extensions/
│   ├── Color+Extensions.swift     # 颜色扩展
│   └── String+Localization.swift  # 本地化扩展
├── ViewModels/
│   └── HomeViewModel.swift        # 首页视图模型
└── zh-Hans.lproj/
    └── Localizable.strings        # 中文本地化字符串
```

## 使用方法

1. 打开Xcode项目
2. 选择iOS模拟器或真机
3. 运行项目
4. 体验液态TabBar的切换效果

## 特色功能

### 液态动画效果
- 平滑的tab切换动画
- 融球跟随手指点击位置移动
- 自然的弹性动画效果

### 响应式布局
- 适配不同屏幕尺寸
- 安全区域自动处理
- 横竖屏切换支持

### 可扩展架构
- 模块化组件设计
- 易于添加新的tab页面
- 统一的状态管理

## 开发环境

- **Xcode**: 15.0+
- **iOS**: 15.6+
- **Swift**: 5.0+
- **SwiftUI**: 3.0+

## 注意事项

1. 项目使用SwiftUI框架，需要iOS 15.6+支持
2. 液态TabBar效果在真机上表现更佳
3. 建议在iPhone 15模拟器上测试以获得最佳体验

## TabBar图标配置

### 自定义图标资源
项目使用专属的图标资源，提供一致的视觉体验：

- **首页图标**: `shouye1_1.png` (选中和未选中状态相同)
- **成长日记图标**:
  - 未选中: `chengzhang.png` (成长图标)
  - 选中: `luyin.png` (录音图标，放大1.3倍显示)
- **个人中心图标**: `wode13.png` (选中和未选中状态相同)

### 图标资源路径
```
Assets.xcassets/
├── shouye1_1.imageset/shouye1_1.png
├── chengzhang.imageset/chengzhang.png
├── 录音.imageset/luyin.png
└── wode13.imageset/wode13.png
```

## 数据模型架构

### Core Data + CloudKit 同步
项目采用 Core Data 作为本地数据存储，集成 CloudKit 实现多设备数据同步。

### 核心实体关系
```
User (用户)
├── Subscription (订阅信息)
├── GlobalRule (全局规则)
└── Member (家庭成员)
    ├── PointRecord (积分记录)
    ├── DiaryEntry (成长日记)
    ├── AIReport (AI报告)
    ├── MemberRule (成员规则)
    ├── MemberPrize (成员奖品)
    ├── RedemptionRecord (兑换记录)
    ├── LotteryRecord (抽奖记录)
    └── LotteryConfig (抽奖配置)
        └── LotteryItem (抽奖项目)
```

### 数据管理组件

#### DataManager.swift
统一的数据管理器，提供以下功能：
- **用户管理**: 用户创建和认证
- **成员管理**: 家庭成员的增删改查
- **积分系统**: 积分记录、规则管理、统计分析
- **奖品系统**: 奖品配置、兑换记录
- **抽奖系统**: 抽奖配置、执行抽奖
- **日记系统**: 成长日记的管理
- **AI报告**: 分析报告的生成和存储
- **订阅管理**: 会员状态和权限控制

#### CoreDataExtensions.swift
为所有实体提供便利属性和方法：
- 计算属性（如年龄计算、积分统计）
- 关系数据的便捷访问
- 格式化显示方法
- 业务逻辑判断方法

### 数据同步特性
- **iCloud 同步**: 支持多设备数据同步
- **离线支持**: 本地数据存储，网络恢复时自动同步
- **冲突解决**: CloudKit 自动处理数据冲突
- **增量同步**: 只同步变更的数据，提高效率

### 使用方法

#### 基本数据操作
```swift
// 获取数据管理器
let dataManager = DataManager.shared

// 创建家庭成员
let member = dataManager.createMember(
    name: "小明",
    role: "son",
    birthDate: Date(),
    initialPoints: 100
)

// 添加积分记录
dataManager.addPointRecord(
    to: member,
    reason: "完成作业",
    value: 10
)

// 创建日记条目
dataManager.createDiaryEntry(
    for: member,
    content: "今天学会了新的数学公式"
)
```

#### 订阅状态检查
```swift
// 检查用户权限
let canUseAI = dataManager.canMemberUseFeature(member, feature: "ai_analysis")
let canUseLottery = dataManager.canMemberUseFeature(member, feature: "wheel")
```

## 安装和运行

### 环境要求
- **Xcode**: 15.0+
- **iOS**: 15.6+
- **Swift**: 5.0+
- **SwiftUI**: 3.0+

### 安装步骤
1. 克隆项目到本地
2. 打开 `ztt2.xcodeproj`
3. 配置 Apple Developer 账号（CloudKit 需要）
4. 选择目标设备或模拟器
5. 运行项目

### CloudKit 配置
1. 在 Apple Developer 中启用 CloudKit
2. 确保项目的 Bundle ID 与开发者账号匹配
3. 在 Xcode 中启用 CloudKit 能力

## 后续开发计划

- [x] 完善数据模型设计
- [x] 实现 Core Data + CloudKit 同步
- [x] 创建数据管理器
- [ ] 完善家庭成员管理功能
- [ ] 实现积分系统业务逻辑
- [ ] 添加抽奖功能实现
- [ ] 集成 AI 分析功能
- [ ] 实现订阅管理
- [ ] 优化液态动画性能
- [ ] 添加更多主题色彩选择

## 当前大模型

本项目由 **Claude Sonnet 4** 开发完成，提供了完整的液态TabBar实现方案和数据模型架构。
